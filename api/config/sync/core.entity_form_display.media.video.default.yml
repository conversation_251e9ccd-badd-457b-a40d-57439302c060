uuid: 9f5f2723-eb53-46bc-b82e-0ec0146cb062
langcode: en
status: true
dependencies:
  config:
    - field.field.media.video.field_media_video_file
    - media.type.video
  module:
    - file
    - path
_core:
  default_config_hash: QcsaoQN46kJyeffkXCqa7aX7pPtpqJjITEqeBse33D0
id: media.video.default
targetEntityType: media
bundle: video
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_media_video_file:
    type: file_generic
    weight: 0
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 100
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  name: true
