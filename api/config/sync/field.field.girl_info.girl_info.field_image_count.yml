uuid: 6a42f72c-4b43-4162-a7c9-6b1f29e8745e
langcode: en
status: true
dependencies:
  config:
    - field.storage.girl_info.field_image_count
  module:
    - pb_girl_info
id: girl_info.girl_info.field_image_count
field_name: field_image_count
entity_type: girl_info
bundle: girl_info
label: 'Image Count'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
