uuid: a014b8f4-f487-456b-8ecd-02627b829d8f
langcode: en
status: true
dependencies:
  config:
    - field.field.user.user.field_active_subscription_cancel
    - field.field.user.user.field_active_subscription_name
    - field.field.user.user.field_active_subscription_paymen
    - field.field.user.user.field_active_subscription_start_
    - field.field.user.user.field_active_subscription_uuid
    - field.field.user.user.field_subscription_data
    - field.field.user.user.field_test
    - field.field.user.user.field_zoneinfo
    - field.field.user.user.user_picture
    - image.style.thumbnail
  module:
    - datetime
    - field_group
    - path
    - svg_image
    - user
third_party_settings:
  field_group:
    group_active_subscription:
      children:
        - field_active_subscription_name
        - field_active_subscription_start_
        - field_active_subscription_cancel
        - field_active_subscription_uuid
        - field_active_subscription_paymen
      label: 'Active Subscription'
      region: content
      parent_name: ''
      weight: 12
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: true
        description: ''
        required_fields: true
_core:
  default_config_hash: K-1rBM8mTIkFp9RqOC2tMRUukOQ1xbRCfSKK8dEddnA
id: user.user.default
targetEntityType: user
bundle: user
mode: default
content:
  account:
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  contact:
    weight: 5
    region: content
  field_active_subscription_cancel:
    type: datetime_default
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  field_active_subscription_name:
    type: string_textfield
    weight: 7
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_active_subscription_paymen:
    type: string_textfield
    weight: 11
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_active_subscription_start_:
    type: datetime_default
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  field_active_subscription_uuid:
    type: string_textfield
    weight: 10
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_subscription_data:
    type: string_textarea
    weight: 13
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_test:
    type: string_textfield
    weight: 4
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_zoneinfo:
    type: string_textarea
    weight: 6
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  language:
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  timezone:
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  user_picture:
    type: image_image
    weight: 1
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
hidden:
  langcode: true
