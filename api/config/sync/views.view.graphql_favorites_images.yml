uuid: 6d28a6cc-0190-4052-87d1-81e42b422b22
langcode: en
status: true
dependencies:
  config:
    - flag.flag.image_flag
    - media.type.image
  module:
    - datetime
    - flag
    - graphql_views
    - media
    - pb_girl
    - pb_girl_info
    - user
id: graphql_favorites_images
label: 'GraphQL / Favorites Images'
module: views
description: ''
tag: ''
base_table: media_field_data
base_field: mid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        name:
          id: name
          table: media_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: media
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            make_link: false
            absolute: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            trim: false
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'view media'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        last_updated:
          id: last_updated
          table: flag_counts
          field: last_updated
          relationship: flag_relationship
          group_type: group
          admin_label: ''
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
          granularity: second
        field_publish_date_value:
          id: field_publish_date_value
          table: media__field_publish_date
          field: field_publish_date_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: datetime
          order: DESC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
          granularity: second
      arguments: {  }
      filters:
        status:
          id: status
          table: media_field_data
          field: status
          entity_type: media
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        bundle:
          id: bundle
          table: media_field_data
          field: bundle
          entity_type: media
          entity_field: bundle
          plugin_id: bundle
          value:
            image: image
          expose:
            operator_limit_selection: false
            operator_list: {  }
        status_1:
          id: status_1
          table: girl_field_data
          field: status
          relationship: girl
          group_type: group
          admin_label: ''
          entity_type: girl
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        status_2:
          id: status_2
          table: girl_info_field_data
          field: status
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        status_3:
          id: status_3
          table: media_field_data
          field: status
          relationship: reverse__media__field_media_slideshow
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        mid:
          id: mid
          table: media_field_data
          field: mid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: mid
          plugin_id: numeric
          operator: regular_expression
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: mid_op
            label: ID
            description: ''
            use_operator: false
            operator: mid_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: image_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        reverse__media__field_media_slideshow:
          id: reverse__media__field_media_slideshow
          table: media_field_data
          field: reverse__media__field_media_slideshow
          relationship: none
          group_type: group
          admin_label: gallery
          entity_type: media
          plugin_id: entity_reverse
          required: true
        reverse__girl_info__galleries:
          id: reverse__girl_info__galleries
          table: media_field_data
          field: reverse__girl_info__galleries
          relationship: reverse__media__field_media_slideshow
          group_type: group
          admin_label: info
          entity_type: media
          plugin_id: entity_reverse
          required: true
        girl:
          id: girl
          table: girl_info_field_data
          field: girl
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: girl
          entity_type: girl_info
          entity_field: girl
          plugin_id: standard
          required: true
        flag_relationship:
          id: flag_relationship
          table: media_field_data
          field: flag_relationship
          relationship: none
          group_type: group
          admin_label: Flags
          entity_type: media
          plugin_id: flag_relationship
          required: true
          flag: image_flag
          user_scope: current
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
  graphql_1:
    id: graphql_1
    display_title: GraphQL
    display_plugin: graphql
    position: 1
    display_options:
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 1000
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      display_extenders: {  }
      graphql_query_name: pbFavoritesImages
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
