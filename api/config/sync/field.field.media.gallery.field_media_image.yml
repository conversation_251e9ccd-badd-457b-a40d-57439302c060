uuid: 0ecef8a8-e7d7-4313-838c-59b3da53a77c
langcode: en
status: true
dependencies:
  config:
    - field.storage.media.field_media_image
    - media.type.gallery
  module:
    - image
id: media.gallery.field_media_image
field_name: field_media_image
entity_type: media
bundle: gallery
label: Image
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: '[date:custom:Y]-[date:custom:m]'
  file_extensions: 'png gif jpg jpeg'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: true
  alt_field_required: false
  title_field: false
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
