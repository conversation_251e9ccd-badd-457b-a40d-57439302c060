uuid: 871fd607-2581-482a-b777-237bff6b5b85
langcode: en
status: true
dependencies:
  config:
    - field.field.taxonomy_term.tags.field_bustsize
    - field.field.taxonomy_term.tags.field_preference
    - taxonomy.vocabulary.tags
  module:
    - path
    - text
id: taxonomy_term.tags.default
targetEntityType: taxonomy_term
bundle: tags
mode: default
content:
  description:
    type: text_textarea
    weight: 3
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_bustsize:
    type: options_select
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  field_preference:
    type: options_select
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 4
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  path:
    type: path
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
hidden: {  }
