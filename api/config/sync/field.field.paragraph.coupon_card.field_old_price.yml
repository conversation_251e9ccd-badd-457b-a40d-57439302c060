uuid: 3e740e83-7ca7-4bb2-9ed7-83ed5dc2a69c
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_old_price
    - paragraphs.paragraphs_type.coupon_card
id: paragraph.coupon_card.field_old_price
field_name: field_old_price
entity_type: paragraph
bundle: coupon_card
label: 'Old Price'
description: 'Der Preis für die gesamte Laufzeit'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
