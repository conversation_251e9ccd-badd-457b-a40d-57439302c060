uuid: 64e2524f-e9c4-4dff-bc18-361aa2aa36aa
langcode: en
status: true
dependencies: {  }
id: user
class: null
field_plugin_method: null
cck_plugin_method: null
migration_tags: null
migration_group: user
label: 'User migration'
source:
  plugin: url
  glob: true
  urls:
    - 'public://migration/girl_info/processed/user/*.json'
  data_fetcher_plugin: file
  data_parser_plugin: json
  item_selector: user
  fields:
    -
      name: uuid
      label: Uuid
      selector: _uuid
    -
      name: blocked
      label: Blocked
      selector: blocked
    -
      name: expired
      label: Expired
      selector: expired
    -
      name: email
      label: Email
      selector: email
    -
      name: pass
      label: Password
      selector: password
    -
      name: created
      label: 'Created date'
      selector: _created
    -
      name: changed
      label: 'Changed date'
      selector: _modified
    -
      name: login
      label: 'Last login time'
      selector: lastLogin
  ids:
    uuid:
      type: string
process:
  uuid: uuid
  name: email
  mail: email
  pass: pass
  created:
    plugin: callback
    callable: pb_girl_info_strtotime_migrate_callback
    source: created
  changed:
    plugin: callback
    callable: pb_girl_info_strtotime_migrate_callback
    source: changed
  login:
    plugin: callback
    callable: pb_girl_info_strtotime_migrate_callback
    source: login
destination:
  plugin: 'entity:user'
migration_dependencies:
  required: {  }
  optional: {  }
