uuid: d4278b96-f366-4470-8a44-6f7dfdf9ed4f
langcode: en
status: true
dependencies:
  config:
    - field.field.media.public_image.field_focal_point_x
    - field.field.media.public_image.field_focal_point_y
    - field.field.media.public_image.field_media_image_1
    - image.style.thumbnail
    - media.type.public_image
  module:
    - image
    - path
id: media.public_image.default
targetEntityType: media
bundle: public_image
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_focal_point_x:
    type: string_textfield
    weight: 101
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_focal_point_y:
    type: string_textfield
    weight: 102
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_media_image_1:
    type: image_image
    weight: 0
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 100
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
