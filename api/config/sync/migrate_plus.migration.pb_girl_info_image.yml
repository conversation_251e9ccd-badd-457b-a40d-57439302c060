uuid: b530524c-8030-4c30-a8d9-b572ce8d0c89
langcode: en
status: true
dependencies: {  }
_core:
  default_config_hash: ESG2x3MSLc7DomtiTVDA4Ye7Bsz0Og_LE1C1h6RQwx0
id: pb_girl_info_image
class: null
field_plugin_method: null
cck_plugin_method: null
migration_tags: null
migration_group: pb_girl_info_image
label: 'Import girl image entities'
source:
  plugin: url
  glob: true
  urls:
    - 'public://migration/girl_info/data/images/*.json'
  data_fetcher_plugin: file
  data_parser_plugin: json
  item_selector: images
  fields:
    -
      name: girl_image_id
      label: 'Primary girl info id'
      selector: id
    -
      name: uuid
      label: Uuid
      selector: uuid
    -
      name: description
      label: description
      selector: description
    -
      name: credit
      label: credit
      selector: credit
    -
      name: image
      label: Image
      selector: file
    -
      name: fsk
      label: fsk
      selector: fsk
    -
      name: focal_x
      label: focal_x
      selector: focalPoint/x
    -
      name: focal_y
      label: focal_y
      selector: focalPoint/y
  ids:
    uuid:
      type: string
  constants:
    file_destination: 'private://'
process:
  mid: girl_image_id
  uuid: uuid
  label: girl_image_id
  field_description: description
  field_credit: credit
  field_focal_point_x: focal_x
  field_focal_point_y: focal_y
  field_fsk:
    -
      plugin: entity_generate
      value_key: name
      bundle_key: vid
      bundle: fsk
      entity_type: taxonomy_term
      source: fsk
  type:
    plugin: default_value
    default_value: image
  _file_destination:
    plugin: concat
    source:
      - constants/file_destination
      - image
  field_media_image:
    plugin: file_remote_image
    source: '@_file_destination'
destination:
  plugin: 'entity:media'
  default_bundle: image
migration_dependencies: {  }
