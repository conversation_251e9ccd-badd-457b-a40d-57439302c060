uuid: 36617742-157d-42b6-a657-e1ab7efdbf17
langcode: en
status: true
dependencies:
  config:
    - field.storage.girl.field_release_date
  module:
    - datetime
    - pb_girl
id: girl.girl.field_release_date
field_name: field_release_date
entity_type: girl
bundle: girl
label: 'Release Date'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: datetime
