uuid: 35bd051b-1d7d-42a9-a9b5-df917bd3e5bd
langcode: en
status: true
dependencies:
  config:
    - field.storage.media.field_preview_image
    - media.type.image
    - media.type.nexx_video
_core:
  default_config_hash: RkrJDJcR6nekCsO13t5OH46I2Y1JqufxeSWpG6hJZFg
id: media.nexx_video.field_preview_image
field_name: field_preview_image
entity_type: media
bundle: nexx_video
label: 'Preview Image'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
    sort:
      field: _none
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
