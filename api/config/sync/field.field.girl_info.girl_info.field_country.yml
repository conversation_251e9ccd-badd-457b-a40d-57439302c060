uuid: 2fefeb0e-ff6a-4e70-b1d9-4e989d4c89ab
langcode: en
status: true
dependencies:
  config:
    - field.storage.girl_info.field_country
    - taxonomy.vocabulary.country
  module:
    - pb_girl_info
id: girl_info.girl_info.field_country
field_name: field_country
entity_type: girl_info
bundle: girl_info
label: Country
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      country: country
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
