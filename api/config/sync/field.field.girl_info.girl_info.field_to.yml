uuid: 4710f77e-38b0-4b68-857b-e326b1c2cea7
langcode: en
status: true
dependencies:
  config:
    - field.storage.girl_info.field_to
  module:
    - datetime
    - pb_girl_info
id: girl_info.girl_info.field_to
field_name: field_to
entity_type: girl_info
bundle: girl_info
label: To
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: datetime
