uuid: f36abd60-9749-4376-a92b-ffff52779cdb
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_hero_slideshow
    - media.type.public_image
    - node.type.homepage
id: node.homepage.field_hero_slideshow
field_name: field_hero_slideshow
entity_type: node
bundle: homepage
label: Slideshow
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      public_image: public_image
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
