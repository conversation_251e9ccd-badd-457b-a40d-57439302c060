uuid: a02f9775-dfe0-4bcb-a973-8de853170000
langcode: en
status: true
dependencies:
  config:
    - node.type.homepage
    - node.type.issue
  module:
    - content_moderation
_core:
  default_config_hash: Ln7YAg2WXZ-5wn9ib-v9qOKFxF2YZLnwSKtX-V455hE
id: editorial
label: Editorial
type: content_moderation
type_settings:
  states:
    archived:
      label: Archived
      weight: 5
      published: false
      default_revision: true
    draft:
      label: Draft
      weight: -5
      published: false
      default_revision: false
    published:
      label: Published
      weight: 0
      published: true
      default_revision: true
  transitions:
    archive:
      label: Archive
      from:
        - published
      to: archived
      weight: 2
    archived_draft:
      label: 'Restore to Draft'
      from:
        - archived
      to: draft
      weight: 3
    archived_published:
      label: Restore
      from:
        - archived
      to: published
      weight: 4
    create_new_draft:
      label: 'Create New Draft'
      from:
        - draft
        - published
      to: draft
      weight: 0
    publish:
      label: Publish
      from:
        - draft
        - published
      to: published
      weight: 1
  entity_types:
    node:
      - homepage
      - issue
  default_moderation_state: draft
