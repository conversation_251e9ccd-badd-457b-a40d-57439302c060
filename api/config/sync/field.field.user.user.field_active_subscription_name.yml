uuid: 56cd3705-8b1b-44c2-a8fc-a8dccc3cb28c
langcode: en
status: true
dependencies:
  config:
    - field.storage.user.field_active_subscription_name
  module:
    - user
id: user.user.field_active_subscription_name
field_name: field_active_subscription_name
entity_type: user
bundle: user
label: Name
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
