uuid: c4f5e342-375b-47db-9914-09802986b40b
langcode: en
status: true
dependencies: {  }
id: likes
class: null
field_plugin_method: null
cck_plugin_method: null
migration_tags: null
migration_group: flags
label: 'User Likes migration'
source:
  plugin: url
  glob: true
  urls:
    - 'public://migration/girl_info/data/user/*.json'
  data_fetcher_plugin: file
  data_parser_plugin: json
  item_selector: likes
  fields:
    -
      name: uuid
      label: Uuid
      selector: _uuid
    -
      name: id
      label: 'Girl/Media id'
      selector: _id
    -
      name: created
      label: 'Created date'
      selector: _created
    -
      name: changed
      label: 'Changed date'
      selector: _modified
    -
      name: entity_type
      label: 'Entity type'
      selector: _entity
    -
      name: user_uuid
      label: 'User uuid'
      selector: user_uuid
  entity_id: null
  ids:
    uuid:
      type: string
    user_uuid:
      type: string
process:
  flag_id:
    plugin: static_map
    source: entity_type
    map:
      GIRL: girl_flag
      MEDIA_IMAGE: image_flag
      girl_info: girl_info_flag
  entity_id:
    plugin: skip_on_empty
    method: row
    source: entity_id
    message: 'Skipped favorites flag: Entity id not found.'
  entity_type:
    plugin: static_map
    source: entity_type
    bypass: true
    map:
      GIRL: girl
      MEDIA_IMAGE: media
  global:
    plugin: default_value
    default_value: 0
  uid:
    plugin: migration_lookup
    migration: user
    source: user_uuid
destination:
  plugin: 'entity:flagging'
migration_dependencies:
  required:
    - user
  optional: {  }
