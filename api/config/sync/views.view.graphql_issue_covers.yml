uuid: 889094fb-d1c4-4c70-b53a-a5a6549b4c5d
langcode: en
status: true
dependencies:
  config:
    - field.storage.media.field_media_image
    - field.storage.node.field_month
    - node.type.issue
    - user.role.subscriber
  module:
    - datetime
    - graphql_views
    - media
    - node
    - rest
    - serialization
    - svg_image
    - user
id: graphql_issue_covers
label: 'GraphQL / Issue Covers'
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        field_media_image:
          id: field_media_image
          table: media__field_media_image
          field: field_media_image
          relationship: field_cover
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: image_url
          settings:
            image_style: ''
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_month:
          id: field_month
          table: node__field_month
          field: field_month
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: datetime_plain
          settings:
            timezone_override: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: role
        options:
          role:
            subscriber: subscriber
            plus: plus
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        created:
          id: created
          table: node_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: created
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: created
          exposed: false
          granularity: second
      arguments: {  }
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          entity_type: node
          entity_field: type
          plugin_id: bundle
          value:
            issue: issue
          expose:
            operator_limit_selection: false
            operator_list: {  }
      style:
        type: serializer
        options:
          formats:
            json: json
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        field_featured_video:
          id: field_featured_video
          table: node__field_featured_video
          field: field_featured_video
          relationship: none
          group_type: group
          admin_label: 'field_featured_video: Media'
          plugin_id: standard
          required: false
        field_cover:
          id: field_cover
          table: node__field_cover
          field: field_cover
          relationship: none
          group_type: group
          admin_label: 'field_cover: Media'
          plugin_id: standard
          required: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - url.query_args
        - 'user.node_grants:view'
        - user.roles
      tags:
        - 'config:field.storage.media.field_media_image'
        - 'config:field.storage.node.field_month'
  graphql_1:
    id: graphql_1
    display_title: GraphQL
    display_plugin: graphql
    position: 2
    display_options:
      pager:
        type: none
        options:
          offset: 0
      row:
        type: graphql_field
        options:
          field_options:
            title:
              alias: ''
              type: String
              raw_output: 0
            field_e_paper:
              alias: ''
              type: String
              raw_output: 0
            field_nexx_id:
              alias: ''
              type: String
              raw_output: 0
            field_month:
              alias: ''
              type: String
              raw_output: 0
            body:
              alias: ''
              type: String
              raw_output: 0
            field_media_image:
              alias: ''
              type: String
              raw_output: 0
      display_extenders: {  }
      graphql_query_name: pbIssueCovers
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.roles
      tags:
        - 'config:field.storage.media.field_media_image'
        - 'config:field.storage.node.field_month'
