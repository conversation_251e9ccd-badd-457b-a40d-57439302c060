uuid: 9c83ae6c-8f8a-41cf-a541-3962967ffb7d
langcode: en
status: true
dependencies:
  config:
    - field.storage.girl_info.field_city
    - taxonomy.vocabulary.city
  module:
    - pb_girl_info
id: girl_info.girl_info.field_city
field_name: field_city
entity_type: girl_info
bundle: girl_info
label: Geburtsort
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      city: city
    sort:
      field: name
      direction: asc
    auto_create: true
    auto_create_bundle: ''
field_type: entity_reference
