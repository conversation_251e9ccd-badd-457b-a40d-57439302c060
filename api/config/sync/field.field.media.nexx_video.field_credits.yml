uuid: e3a8a2d1-0516-4e21-a258-c1617fe1dada
langcode: en
status: true
dependencies:
  config:
    - field.storage.media.field_credits
    - media.type.nexx_video
    - taxonomy.vocabulary.credit
id: media.nexx_video.field_credits
field_name: field_credits
entity_type: media
bundle: nexx_video
label: Credits
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      credit: credit
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
