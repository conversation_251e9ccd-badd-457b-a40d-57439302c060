uuid: c4a8ea79-a5dd-4050-9d1c-8a587a3bdf9a
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_month
    - paragraphs.paragraphs_type.coupon_card
  module:
    - options
id: paragraph.coupon_card.field_month
field_name: field_month
entity_type: paragraph
bundle: coupon_card
label: Month
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: list_string
