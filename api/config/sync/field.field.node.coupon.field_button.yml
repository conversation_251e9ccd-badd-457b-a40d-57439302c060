uuid: 9ba5f762-adbc-401a-bcde-6cb5a339acaf
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_button
    - node.type.coupon
  module:
    - link
id: node.coupon.field_button
field_name: field_button
entity_type: node
bundle: coupon
label: Button
description: '<PERSON><PERSON> kein <PERSON> angeze<PERSON>t werden soll, <PERSON><PERSON> fre<PERSON>. '
required: false
translatable: true
default_value:
  -
    attributes: {  }
    uri: 'https://api.premium.playboy.de/user/login'
    title: '<PERSON>zt freischalten'
    options: {  }
default_value_callback: ''
settings:
  title: 1
  link_type: 17
field_type: link
