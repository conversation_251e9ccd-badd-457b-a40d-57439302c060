uuid: b742762c-ad3f-4b53-9b8f-52186576f72a
langcode: en
status: true
dependencies:
  module:
    - language
_core:
  default_config_hash: i0rlksghfAqbopMmmoAR1IEzR3yTsE8cq8rYefYoSwM
id: language-add
label: 'Adding languages'
module: language
routes:
  -
    route_name: language.add
tips:
  language-add-overview:
    id: language-add-overview
    plugin: text
    label: 'Adding languages'
    weight: 1
    body: '<p>This page provides the ability to add common languages to your site.</p><p>If the desired language is not available, you can add a custom language.</p>'
  language-add-choose:
    id: language-add-choose
    plugin: text
    label: 'Select language'
    weight: 2
    selector: '#edit-predefined-langcode'
    body: '<p>Choose a language from the list, or choose "Custom language..." at the end of the list.</p><p>Click the "Add language" button when you are done choosing your language.</p><p>When adding a custom language, you will get an additional form where you can provide the name, code, and direction of the language.</p>'
  language-add-continue:
    id: language-add-continue
    plugin: text
    label: 'Continuing on'
    weight: 3
    body: '<p>Now that you have an overview of the "Add languages" feature, you can continue by:<ul><li>Adding a language</li><li>Adding a custom language</li><li><a href="[site:url]admin/config/regional/language">Viewing configured languages</a></li></ul></p>'
