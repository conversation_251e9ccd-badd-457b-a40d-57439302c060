uuid: 3d4a48b4-b97c-4944-bf65-77ac6d9de949
langcode: en
status: true
dependencies:
  config:
    - field.field.node.static_page.field_body
    - field.field.node.static_page.field_url
    - node.type.static_page
  module:
    - content_moderation
    - path
    - scheduler
    - scheduler_content_moderation_integration
    - text
id: node.static_page.default
targetEntityType: node
bundle: static_page
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_body:
    type: text_textarea
    weight: 121
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_url:
    type: string_textfield
    weight: 122
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: 100
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 15
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  publish_on:
    type: datetime_timestamp_no_default
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  publish_state:
    type: scheduler_moderation
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  scheduler_settings:
    weight: 20
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 120
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 16
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  unpublish_on:
    type: datetime_timestamp_no_default
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  unpublish_state:
    type: scheduler_moderation
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
hidden: {  }
