uuid: b0eebf99-f991-4b21-b014-94602b2da833
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_coupon
    - paragraphs.paragraphs_type.coupon_card
id: paragraph.coupon_card.field_coupon
field_name: field_coupon
entity_type: paragraph
bundle: coupon_card
label: Coupon
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
