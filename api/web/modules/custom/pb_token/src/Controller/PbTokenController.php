<?php

/**
 * @file
 * Contains \Drupal\pb_flag\Controller\PbFlagController.
 */

namespace Drupal\pb_token\Controller;

use <PERSON><PERSON>al\Core\Controller\ControllerBase;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Dr<PERSON>al\user\Entity\User;
use Drupal\openid_connect\OpenIDConnect;
use Drupal\Core\Routing\TrustedRedirectResponse;
use Drupal\openid_connect\OpenIDConnectStateToken;
use Drupal\Core\Database\Database;
use Symfony\Component\HttpFoundation\RedirectResponse;

/**
 * Controller routines for pb_token routes.
 */
class PbTokenController extends ControllerBase
{

  private $timeout = 600;

  /**
   * Callback for `api/post.json` API method.
   */
  public function new()
  {
    $platformsh = new \Platformsh\ConfigReader\Config();
    if ($platformsh->branch == 'master') {
      $url = 'https://sso.playboy.de/auth/realms/playboy/protocol/openid-connect/auth?client_id=CMSPlayboy&response_type=code&scope=openid%20email%20profile&redirect_uri=https%3A//' . $_SERVER['HTTP_HOST'] . '/openid-connect/keycloak&state=' . OpenIDConnectStateToken::create();
    } else {
      $url = 'https://playboy.sso.stage.cover-ecom.de/auth/realms/playboy/protocol/openid-connect/auth?client_id=CMSPlayboy&response_type=code&scope=openid%20email%20profile&redirect_uri=https%3A//' . $_SERVER['HTTP_HOST'] . '/openid-connect/keycloak&state=' . OpenIDConnectStateToken::create();
    }
    header('Location: ' . $url);
    exit;
  }
  public function refresh(Request $request)
  {
    if (!is_null(\Drupal::currentUser()->id())) {
      $session_manager = \Drupal::service('session_manager');
      $session_manager->delete(\Drupal::currentUser()->id());
      return $this->redirect('<front>');
    }
    return $this->redirect('/user/token/logout');
  }
  public function logout(Request $request)
  {
    if (!is_null(\Drupal::currentUser()->id())) {
      $session_manager = \Drupal::service('session_manager');
      $session_manager->delete(\Drupal::currentUser()->id());
    }
    return $this->redirect('<front>');
  }

  public function getToken($path)
  {
    return $this->_sign_bcdn_url(
      'https://playboy-premium.b-cdn.net/', // Url to sign
      "d575d3d8-a429-420b-af10-ea8fc292c597", // Token Key
      $this->timeout,
      NULL,
      true,
      $path
    );
  }

  public function cancel(Request $request)
  {

    \Drupal::logger('pb_token')->notice("Cancel initiated.");

    $client = \Drupal::httpClient();
    $connection = Database::getConnection();
    $current_user = \Drupal::currentUser();
    $user = \Drupal\user\Entity\User::load($current_user->id());

    if (is_null($request->get('sub'))) {
      \Drupal::logger('pb_token')->error("No UUID given.");
      return $this->redirect('<front>');
    }

    $sub = $request->get('sub');

    if (is_null($user->id())) {
      \Drupal::logger('pb_token')->error("Drupal User ID null");
      return $this->redirect('<front>');
    }

    \Drupal::logger('pb_token')->notice("Drupal User ID " . $user->id());
    \Drupal::logger('pb_token')->notice("Sub: " . $sub);

    $subscription_data = json_decode($user->get('field_subscription_data')->value, true);

    if (!empty($subscription_data)) {
      foreach ($subscription_data as $key1 => $openid_subscription) {
        foreach ($openid_subscription as $key2 => $openid_inner_subscription) {
          if ($openid_inner_subscription['uuid'] == $sub) {

            $response = $client->post('https://webservices.covernet.de:8443/mex/wrd/ecattrcancel', [
              'headers' => [
                'C_BENUTZER_ID' => 'IMS4WS',
                'C_PASSWORT' => 'KNF4ZN83z0RT',
                'C_TRANSFER_ID' => 'COVERNET2_KM',
                'C_MANDANT' => 'KM',
                'C_ANW' => 'V',
                'C_KUNDE' => 'MEX',
                'Content-Type' => 'application/json'
              ],
              'body' => '{"attr_cancel":{"transaction":{"timestamp":"20180306192355","transaction_id":"PUBL00000556647","ext_system_type":"WEBSHOP","ext_system_id":"magento2.publisher.comwrap.host","ext_system_name":"COVER-Web-Shop.de"},"customer_attributes":[{"timestamp":"20180306192355","attribute_status":"REQUEST","attribute_type":"SUBSCR","uuid_orderpos":"' . $sub . '"}]}}'
            ]);

            \Drupal::logger('pb_token')->notice("Response code: " . $response->getStatusCode());
            $raw = $response->getBody()->getContents();
            $content = json_decode($raw, true);

            if ($response->getStatusCode() !== 200) {
              \Drupal::logger('pb_token')->error("Response: " . $raw);
            } else {
              \Drupal::logger('pb_token')->notice("Response: " . $raw);


              if (!isset($content['attr_cancel']['customer_attributes']) || count($content['attr_cancel']['customer_attributes']) < 1) {
                \Drupal::logger('pb_token')->error("No customer attributes available.");
              } else {

                foreach ($content['attr_cancel']['customer_attributes'] as $info_subscription) {
                  if (
                    isset($info_subscription['uuid_orderpos'], $openid_inner_subscription['uuid'], $info_subscription['attribute_end_date'])
                    && $info_subscription['uuid_orderpos'] === $openid_inner_subscription['uuid']
                  ) {

                    $newDate = date("Y-m-d", strtotime($info_subscription['attribute_end_date']));
                    $subscription_data[$key1][$key2]['cancelDate'] = strlen($info_subscription['attribute_end_date']) == 10 ? $newDate . ' 23:59:59' : $newDate;
                  }
                }

                $user->set('field_subscription_data', json_encode($subscription_data));
                $user->save();
              }
            }
          }
        }
      }
    }

    \Drupal::logger('pb_token')->notice("Cancel completed ");
    return new RedirectResponse('/profile');
  }

  public function sign(Request $request)
  {
    $path = false;

    if (\Drupal::currentUser()->hasPermission('view media')) {
      $path = '/';
    } elseif (\Drupal::currentUser()->hasPermission('view published plus gallery entities')) {
      $path = '/plus';
    }

    if ($path === false) {
      header('HTTP/1.0 403 Forbidden');
      echo "Forbidden";
      die();
    }

    $url = $this->getToken($path);

    return new JsonResponse([
      'data' => $url,
      'method' => 'GET',
    ]);
  }

  public function image(Request $request)
  {
    // If the current user has the permission, do not hide the Meta vertical tab.
    if (!\Drupal::currentUser()->hasPermission('view media') || is_null($request->query->get('url'))) {
      header('HTTP/1.0 403 Forbidden');
      echo "Forbidden";
      die();
    }

    $url = $this->_sign_bcdn_url(
      $request->query->get('url'), // Url to sign
      "d575d3d8-a429-420b-af10-ea8fc292c597", // Token Key
      $this->timeout,
      NULL,
      true,
      '/'
    );

    if (!is_null($request->query->get('width'))) {
      $url .= '&width=' . $request->query->get('width');
    }

    return new TrustedRedirectResponse($url);
  }

  private function _token_bcdn_url($url, $securityKey, $expiration_time = 3600, $user_ip = NULL, $is_directory_token = false, $path_allowed = NULL, $countries_allowed = NULL, $countries_blocked = NULL, $referers_allowed = NULL)
  {
    if (!is_null($countries_allowed)) {
      $url .= (parse_url($url, PHP_URL_QUERY) == "") ? "?" : "&";
      $url .= "token_countries={$countries_allowed}";
    }
    if (!is_null($countries_blocked)) {
      $url .= (parse_url($url, PHP_URL_QUERY) == "") ? "?" : "&";
      $url .= "token_countries_blocked={$countries_blocked}";
    }
    if (!is_null($referers_allowed)) {
      $url .= (parse_url($url, PHP_URL_QUERY) == "") ? "?" : "&";
      $url .= "token_referer={$referers_allowed}";
    }

    $url_scheme = parse_url($url, PHP_URL_SCHEME);
    $url_host = parse_url($url, PHP_URL_HOST);
    $url_path = parse_url($url, PHP_URL_PATH);
    $url_query = parse_url($url, PHP_URL_QUERY);


    $parameters = array();
    parse_str($url_query, $parameters);

    // Check if the path is specified and ovewrite the default
    $signature_path = $url_path;

    if (!is_null($path_allowed)) {
      $signature_path = $path_allowed;
      $parameters["token_path"] = $signature_path;
    }

    // Expiration time
    $expires = time() + $expiration_time;

    // Construct the parameter data
    ksort($parameters); // Sort alphabetically, very important
    $parameter_data = "";
    $parameter_data_url = "";
    if (sizeof($parameters) > 0) {
      foreach ($parameters as $key => $value) {
        if (strlen($parameter_data) > 0)
          $parameter_data .= "&";

        $parameter_data_url .= "&";

        $parameter_data .= "{$key}=" . $value;
        $parameter_data_url .= "{$key}=" . urlencode($value); // URL encode everything but slashes for the URL data
      }
    }

    // Generate the toke
    $hashableBase = $securityKey . $signature_path . $expires;

    // If using IP validation
    if (!is_null($user_ip)) {
      $hashableBase .= $user_ip;
    }

    $hashableBase .= $parameter_data;

    // Generate the token
    $token = hash('sha256', $hashableBase, true);
    $token = base64_encode($token);
    $token = strtr($token, '+/', '-_');
    $token = str_replace('=', '', $token);

    return [
      'token' => $token . $parameter_data_url,
      'expires' => $expires
    ];
  }

  private function _sign_bcdn_url($url, $securityKey, $expiration_time = 3600, $user_ip = NULL, $is_directory_token = false, $path_allowed = NULL, $countries_allowed = NULL, $countries_blocked = NULL, $referers_allowed = NULL)
  {
    if (!is_null($countries_allowed)) {
      $url .= (parse_url($url, PHP_URL_QUERY) == "") ? "?" : "&";
      $url .= "token_countries={$countries_allowed}";
    }
    if (!is_null($countries_blocked)) {
      $url .= (parse_url($url, PHP_URL_QUERY) == "") ? "?" : "&";
      $url .= "token_countries_blocked={$countries_blocked}";
    }
    if (!is_null($referers_allowed)) {
      $url .= (parse_url($url, PHP_URL_QUERY) == "") ? "?" : "&";
      $url .= "token_referer={$referers_allowed}";
    }

    $url_scheme = parse_url($url, PHP_URL_SCHEME);
    $url_host = parse_url($url, PHP_URL_HOST);
    $url_path = parse_url($url, PHP_URL_PATH);
    $url_query = parse_url($url, PHP_URL_QUERY);


    $parameters = array();
    parse_str($url_query, $parameters);

    // Check if the path is specified and ovewrite the default
    $signature_path = $url_path;

    if (!is_null($path_allowed)) {
      $signature_path = $path_allowed;
      $parameters["token_path"] = $signature_path;
    }

    // Expiration time
    $expires = time() + $expiration_time;

    // Construct the parameter data
    ksort($parameters); // Sort alphabetically, very important
    $parameter_data = "";
    $parameter_data_url = "";
    if (sizeof($parameters) > 0) {
      foreach ($parameters as $key => $value) {
        if (strlen($parameter_data) > 0)
          $parameter_data .= "&";

        $parameter_data_url .= "&";

        $parameter_data .= "{$key}=" . $value;
        $parameter_data_url .= "{$key}=" . urlencode($value); // URL encode everything but slashes for the URL data
      }
    }

    // Generate the toke
    $hashableBase = $securityKey . $signature_path . $expires;

    // If using IP validation
    if (!is_null($user_ip)) {
      $hashableBase .= $user_ip;
    }

    $hashableBase .= $parameter_data;

    // Generate the token
    $token = hash('sha256', $hashableBase, true);
    $token = base64_encode($token);
    $token = strtr($token, '+/', '-_');
    $token = str_replace('=', '', $token);

    if ($is_directory_token) {
      return "{$url_scheme}://{$url_host}/bcdn_token={$token}&expires={$expires}{$parameter_data_url}{$url_path}";
    } else {
      return "{$url_scheme}://{$url_host}{$url_path}?token={$token}{$parameter_data_url}&expires={$expires}";
    }
  }
}
