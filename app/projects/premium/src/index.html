<!doctype html>
<html lang="de">
  <head>
    <meta charset="utf-8" />
    <title>Das Paradies hat eine Homepage - PlayboyPremium</title>
    <meta name="viewport" content="width=device-width" />
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico" />
    <!-- <link rel="preload" href="https://gdpr.privacymanager.io/latest/gdpr.bundle.js" as="script" />
  <script
    src="https://gdpr-wrapper.privacymanager.io/gdpr/************************************/gdpr-liveramp.js"></script> -->
    <script src="https://www.googletagmanager.com/gtag/js?id=UA-152559935-3"></script>
    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-MD6CBT2");
    </script>
    <!-- End Google Tag Manager -->
    <script>
      (function (u, p, s, c, r) {
        (u[r] =
          u[r] ||
          function (p) {
            (u[r].q = u[r].q || []).push(p);
          }),
          (u[r].ls = 1 * new Date());
        var a = p.createElement(s),
          m = p.getElementsByTagName(s)[0];
        a.async = 1;
        a.src = c;
        m.parentNode.insertBefore(a, m);
      })(
        window,
        document,
        "script",
        "//files.upscore.com/async/upScore.js",
        "upScore",
      );
    </script>
    <style>
      app-root {
        opacity: 0;
        overflow-y: hidden;
      }
    </style>
  </head>

  <body>
    <app-root></app-root>
    <script
      src="https://arc.nexx.cloud/sdk/12025.play"
      type="text/javascript"
    ></script>
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-MD6CBT2"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      >
      </iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <script>
      "use strict";
      function _typeof(t) {
        return (_typeof =
          "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
            ? function (t) {
                return typeof t;
              }
            : function (t) {
                return t &&
                  "function" == typeof Symbol &&
                  t.constructor === Symbol &&
                  t !== Symbol.prototype
                  ? "symbol"
                  : typeof t;
              })(t);
      }
      !(function () {
        var t = function () {
          var t,
            e,
            o = [],
            n = window,
            r = n;
          for (; r; ) {
            try {
              if (r.frames.__tcfapiLocator) {
                t = r;
                break;
              }
            } catch (t) {}
            if (r === n.top) break;
            r = r.parent;
          }
          t ||
            (!(function t() {
              var e = n.document,
                o = !!n.frames.__tcfapiLocator;
              if (!o)
                if (e.body) {
                  var r = e.createElement("iframe");
                  (r.style.cssText = "display:none"),
                    (r.name = "__tcfapiLocator"),
                    e.body.appendChild(r);
                } else setTimeout(t, 5);
              return !o;
            })(),
            (n.__tcfapi = function () {
              for (
                var t = arguments.length, n = new Array(t), r = 0;
                r < t;
                r++
              )
                n[r] = arguments[r];
              if (!n.length) return o;
              "setGdprApplies" === n[0]
                ? n.length > 3 &&
                  2 === parseInt(n[1], 10) &&
                  "boolean" == typeof n[3] &&
                  ((e = n[3]), "function" == typeof n[2] && n[2]("set", !0))
                : "ping" === n[0]
                  ? "function" == typeof n[2] &&
                    n[2]({ gdprApplies: e, cmpLoaded: !1, cmpStatus: "stub" })
                  : o.push(n);
            }),
            n.addEventListener(
              "message",
              function (t) {
                var e = "string" == typeof t.data,
                  o = {};
                if (e)
                  try {
                    o = JSON.parse(t.data);
                  } catch (t) {}
                else o = t.data;
                var n =
                  "object" === _typeof(o) && null !== o ? o.__tcfapiCall : null;
                n &&
                  window.__tcfapi(
                    n.command,
                    n.version,
                    function (o, r) {
                      var a = {
                        __tcfapiReturn: {
                          returnValue: o,
                          success: r,
                          callId: n.callId,
                        },
                      };
                      t &&
                        t.source &&
                        t.source.postMessage &&
                        t.source.postMessage(e ? JSON.stringify(a) : a, "*");
                    },
                    n.parameter,
                  );
              },
              !1,
            ));
        };
        "undefined" != typeof module ? (module.exports = t) : t();
      })();
    </script>

    <script>
      window._sp_queue = [];
      window._sp_ = {
        config: {
          accountId: 1895,
          baseEndpoint: "https://cdn.privacy-mgmt.com",

          gdpr: {},
          events: {
            onMessageChoiceSelect: function () {
              console.log("[event] onMessageChoiceSelect", arguments);
            },
            onMessageReady: function () {
              console.log("[event] onMessageReady", arguments);
            },
            onMessageChoiceError: function () {
              console.log("[event] onMessageChoiceError", arguments);
            },
            onPrivacyManagerAction: function () {
              console.log("[event] onPrivacyManagerAction", arguments);
            },
            onPMCancel: function () {
              console.log("[event] onPMCancel", arguments);
            },
            onMessageReceiveData: function () {
              console.log("[event] onMessageReceiveData", arguments);
            },
            onSPPMObjectReady: function () {
              console.log("[event] onSPPMObjectReady", arguments);
            },
            onConsentReady: function (consentUUID, euconsent) {
              console.log("[event] onConsentReady", arguments);
            },
            onError: function () {
              console.log("[event] onError", arguments);
            },
          },
        },
      };
    </script>
    <script
      src="https://cdn.privacy-mgmt.com/unified/wrapperMessagingWithoutDetection.js"
      async
    ></script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        var privacyEditLink = document.querySelector("a#privacyEdit");
        if (privacyEditLink) {
          privacyEditLink.addEventListener("click", function (e) {
            e.preventDefault();
            window._sp_.gdpr.loadPrivacyManagerModal(869023, "vendors");
          });
        }
      });
    </script>
  </body>
</html>
