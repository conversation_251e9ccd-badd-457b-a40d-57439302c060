import {
  PublicImage,
  PublicImageDerivative,
} from '../screens/model/screens/model/definitions/models';

export interface IPreview {
  credit?: string;
  id: string | number;
  girlInfoId?: string | number;
  girlId?: string | number;
  fieldPlusAccess?: boolean | string;
  publicImage?: string;
  text: string;
  title: string;
  subtitle?: string;
  month?: string;
  year?: number;
  nexxID?: string;
  imageRatio?: number;
  image: string;
  imageLowRes?: string;
  isNew?: boolean;
  focalPoint?: { x: number; y: number };
  link?: (string | number)[];
  meta?: {
    videos: number;
    images: number;
    girlInfos: number;
  };
  paywallImages?: Array<PublicImage | PublicImageDerivative>;
}
