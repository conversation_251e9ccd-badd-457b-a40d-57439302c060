<div class="flex flex-col gap-6 md:gap-10">
  @if (title) {
    <h2 class="py-4 w-full text-center">{{ title }}</h2>
  }
  @if (!($loading | async)) {
    @if (($hasFavorites | async) === false) {
      <p class="text-center">Sie haben noch keine Favoriten gesetzt</p>
    } @else if (($hasFavorites | async) === true) {
      <div class="flex flex-col gap-6">
        @if (isGirlsVisible && $girls | async; as girls) {
          @if (girls.length > 0) {
            <article class="w-full flex flex-col gap-6">
              <h3 class="py-4 w-full text-center">Women</h3>
              <div
                class="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-6 gallery-list"
              >
                @for (article of girls; track article; let index = $index) {
                  @if (index < pageSize) {
                    <a class="flex" [routerLink]="article.link">
                      <app-gallery-card
                        class="w-full"
                        [paywallImages]="article?.paywallImages || []"
                        [favoriteType]="'girl'"
                        [previewData]="article"
                        [image]="article.publicImage"
                      ></app-gallery-card>
                    </a>
                  }
                }
              </div>
            </article>
          }
        }

        @if (isGirlInfosVisible && $girlInfos | async; as girlInfos) {
          @if (girlInfos.length > 0) {
            <article class="w-full flex flex-col gap-6">
              <h3 class="py-4 w-full text-center">Galerien</h3>
              <div
                class="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-6 gallery-list"
              >
                @for (article of girlInfos; track article; let index = $index) {
                  @if (index < pageSize) {
                    <a class="flex" [routerLink]="article.link">
                      <app-gallery-card
                        class="w-full"
                        [paywallImages]="article?.paywallImages || []"
                        [isAAContent]="article?.fieldPlusAccess !== true"
                        [previewData]="article"
                        [image]="article.publicImage"
                      ></app-gallery-card>
                    </a>
                  }
                }
              </div>
            </article>
          }
        }

        @if (isVideosVisible && $videos | async; as videos) {

          @if (videos.length > 0) {
            <article class="w-full flex flex-col gap-6">
              <h3 class="py-4 w-full text-center">Videos</h3>
              <div
                class="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-6 gallery-list"
              >
                @for (article of videos; track article; let index = $index) {
                  @if (index < pageSize) {
                    <a class="flex" [routerLink]="article.link">
                      <app-article-preview
                        class="flex w-full rounded-lg overflow-hidden"
                        [previewData]="article"
                        [autoSize]="false"
                      >
                      </app-article-preview>
                    </a>
                  }
                }
              </div>
            </article>
          }
        }
      </div>
      <div class="flex px-3 md:px-0 justify-center">
        <a
          routerLink="/favorites"
          class="uppercase font-inter text-xs lg:text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
        >
          Zu Favoriten
        </a>
      </div>
    }
  }
</div>
