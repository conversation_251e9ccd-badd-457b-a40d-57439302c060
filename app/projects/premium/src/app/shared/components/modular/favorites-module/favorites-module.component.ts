import { Component, Input, OnInit } from '@angular/core';
import { combineLatest, Observable, of } from 'rxjs';
import { IPreview } from '../../../../models/preview';
import { AsyncPipe } from '@angular/common';
import { RouterLink } from '@angular/router';
import { ArticlePreviewComponent } from '../../article-preview/article-preview.component';
import { GalleryCardComponent } from '../../gallery-card/gallery-card.component';
import { FavoritesService } from '../../../../services/favorites/favorites.service';
import { map, startWith } from 'rxjs/operators';

@Component({
  selector: 'favorites-module',
  templateUrl: './favorites-module.component.html',
  styleUrls: ['./favorites-module.component.css'],
  imports: [
    AsyncPipe,
    RouterLink,
    ArticlePreviewComponent,
    GalleryCardComponent,
  ],
  standalone: true,
})
export class FavoritesModuleComponent implements OnInit {
  @Input() title = '';

  @Input() isGirlsVisible = false;
  @Input() isGirlInfosVisible = false;
  @Input() isVideosVisible = false;
  @Input() pageSize = 4;

  $girls: Observable<IPreview[]>;
  $girlInfos: Observable<IPreview[]>;
  $videos: Observable<IPreview[]>;
  $hasFavorites: Observable<boolean>;
  $loading: Observable<boolean>;

  constructor(private favoritesService: FavoritesService) {}

  ngOnInit(): void {
    this.$girls = this.isGirlsVisible
      ? this.favoritesService.getPreviews('girl', { pageSize: this.pageSize })
      : of([]);

    this.$girlInfos = this.isGirlInfosVisible
      ? this.favoritesService.getPreviews('girl-infos', {
          pageSize: this.pageSize,
        })
      : of([]);

    this.$videos = this.isVideosVisible
      ? this.favoritesService.getPreviews('video', { pageSize: this.pageSize })
      : of([]);

    this.$hasFavorites = combineLatest([
      this.$girls,
      this.$girlInfos,
      this.$videos,
    ]).pipe(
      map(([girls, girlInfos, videos]) => {
        const girlsLength = girls?.length || 0;
        const girlInfosLength = girlInfos?.length || 0;
        const videosLength = videos?.length || 0;

        const totalFavorites = girlsLength + girlInfosLength + videosLength;
        return totalFavorites > 0;
      }),
    );

    this.$loading = this.$hasFavorites.pipe(
      map(() => false),
      startWith(true),
    );
  }
}
