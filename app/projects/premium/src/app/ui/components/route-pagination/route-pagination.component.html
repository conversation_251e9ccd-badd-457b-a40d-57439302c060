<a
  class="cursor-pointer rounded-lg overflow-hidden mx-2 bg-gray-800 p-2 hover:shadow-md hover:bg-gray-750 transition w-10 h-10 flex justify-center items-center"
  [routerLink]="type === 'route' ? [baseRoute, prev] : '.'"
  [queryParams]="{ page: prev }"
  queryParamsHandling="merge"
  routerLinkActive="opacity-50 pointer-events-none"
  (click)="animate()"
>
  <img src="assets/arrow-left-small.svg" class="" />
</a>
<div
  class="flex flex-row bg-gray-800 text-gray-400 rounded-lg text-sm overflow-hidden"
  (clickOutside)="openIndex = -1"
>
  @for (
    item of dots;
    track item;
    let first = $first;
    let last = $last;
    let index = $index
  ) {
    @if (!Array.isArray(item)) {
      <a
        class="leading-none inline-flex justify-center items-center text-center cursor-pointer px-2 rounded-lg hover:text-gray-200 hover:bg-gray-750 transition hover:shadow-md min-w-10"
        [routerLink]="type === 'route' ? [baseRoute, item] : '.'"
        [queryParams]="{ page: item }"
        queryParamsHandling="merge"
        routerLinkActive
        #rla="routerLinkActive"
        [class.bg-gray-750]="!!rla.isActive"
        (click)="animate()"
        >{{ item + 1 }}</a
      >
    }
    @if (Array.isArray(item)) {
      <div class="relative">
        <div
          class="w-10 h-full rounded-lg cursor-pointer leading-none inline-flex hover:text-gray-200 justify-center items-center p-2 hover:bg-gray-750 hover:shadow-md transition"
          (click)="openIndex = openIndex === index ? -1 : index"
        >
          ...
        </div>
        @if (openIndex === index) {
          <app-route-pagination-popup
            [type]="type"
            [indexes]="item"
            [baseRoute]="baseRoute"
            class="absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 overflow-hidden z-40"
            >^
          </app-route-pagination-popup>
        }
      </div>
    }
  }
</div>
<a
  class="cursor-pointer rounded-lg overflow-hidden mx-2 bg-gray-800 w-10 h-10 flex justify-center items-center p-2 hover:bg-gray-750 transition"
  [routerLink]="type === 'route' ? [baseRoute, next] : '.'"
  [queryParams]="{ page: next }"
  queryParamsHandling="merge"
  routerLinkActive="opacity-50 pointer-events-none"
  (click)="animate()"
>
  <img src="assets/arrow-right-small.svg" class="" />
</a>
<!--
<div class="flex flex-row justify-center mt-4" *ngIf="$paginationMeta | async as meta">
</div> -->
